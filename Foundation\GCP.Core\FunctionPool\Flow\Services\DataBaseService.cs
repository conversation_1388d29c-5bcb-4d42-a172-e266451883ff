﻿using Acornima.Ast;
using EasyCaching.Core;
using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common;
using GCP.Functions.Common.Services;
using Jint;

namespace GCP.FunctionPool.Flow.Services
{
    public class DataBaseService : IFunctionService
    {
        public FunctionContext Context { get; set; }
        public IEasyCachingProvider Cache { get; set; }

        internal DbContext GetDataContext(string dataSourceId, bool setContextDb = true)
        {
            if (dataSourceId == null)
            {
                throw new ArgumentException("数据源Id不能为空");
            }

            var dataSourceService = new DataSourceService();
            var dataSource = dataSourceService.GetRawById(dataSourceId);

            if (dataSource == null)
            {
                throw new CustomException("数据源不存在");
            }

            var db = new DbContext();

            db.SetDefaultConnection(dataSource.ConnectionString, dataSource.DataProvider);

            if (setContextDb)
            {
                this.Context.LocalDbContext.Value = db;
            }
            return db;
        }

        internal object GetVariableValueByPath(DataValue value, Dictionary<string, object> localVariable = null)
        {
            return FlowUtils.GetVariableValueByPath(value, this.Context.globalData, localVariable);
        }

        //private Dictionary<string, Prepared<Script>> prepareScripts = new();
        private readonly Dictionary<string, Prepared<Script>> _prepared = new();
        internal Engine GetEngine()
        {
            return FlowUtils.GetEngine(this.Context);
        }

        internal object GetDataValue(DataValue value, Engine engine = null, Dictionary<string, object> localVariable = null, string type = null)
        {
            if (value == null) return null;
            if (type == null && value?.Type == "variable")
            {
                type = value.DataType;
            }
            if (!string.IsNullOrEmpty(type))
            {
                return FlowUtils.GetDataValue(value, type, this.Context, engine, localVariable);
            }
            return FlowUtils.GetDataValue(value, engine, this.Context.globalData, localVariable);
        }
    }
}
