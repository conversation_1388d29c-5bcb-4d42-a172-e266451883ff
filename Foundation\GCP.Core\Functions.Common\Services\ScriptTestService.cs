using GCP.Common;
using GCP.Functions.Common;
using Jint;
using System.Diagnostics;
using GCP.FunctionPool.Flow;
using GCP.Functions.Common.ScriptExtensions;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// 脚本测试服务
    /// </summary>
    [Function("script", "脚本测试服务")]
    internal class ScriptTestService : BaseService
    {
        /// <summary>
        /// 测试执行JavaScript脚本
        /// </summary>
        /// <param name="script">要执行的脚本代码</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>执行结果</returns>
        [Function("test", "测试执行脚本")]
        public object Test(string script, object inputData = null)
        {
            if (string.IsNullOrWhiteSpace(script))
            {
                throw new CustomException("脚本内容不能为空");
            }

            var stopwatch = Stopwatch.StartNew();
            var executionId = TUID.NewTUID().ToString();

            try
            {
                // 创建JavaScript引擎
                var engine = FlowUtils.GetEngine(this.Context);
                
                // 注入Utils工具类
                using var db = this.GetDb();
                engine.SetValue("Utils", new JavascriptUtils());
                
                // 注入输入数据
                if (inputData != null)
                {
                    // 如果输入数据是字典类型，将每个键值对注入到引擎中
                    if (inputData is Dictionary<string, object> inputDict)
                    {
                        foreach (var kvp in inputDict)
                        {
                            engine.SetValue(kvp.Key, kvp.Value);
                        }
                    }
                    else
                    {
                        // 否则将整个对象作为data变量注入
                        engine.SetValue("data", inputData);
                    }
                }

                // 执行脚本
                var jsValue = engine.Evaluate(script);
                var result = jsValue.ToObject();

                stopwatch.Stop();

                return new
                {
                    Success = true,
                    Result = result,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    ExecutionId = executionId
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                return new
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    ExecutionId = executionId
                };
            }
        }
    }
}
