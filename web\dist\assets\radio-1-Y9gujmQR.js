import{d,D as a,aa as O,ab as y,ac as C}from"./index-BfYxpJCx.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.3416 2.44706L6.23607 5.99985H23V21.9998H22V20.9998C22 21.9998 22.0001 21.9998 22 21.9998H1L1 6.38181L12.4472 0.658203L13.3416 2.44706ZM21 19.9998V7.99985L3 7.99984L3 19.9998H21ZM9 11.9998C7.89543 11.9998 7 12.8953 7 13.9998C7 15.1044 7.89543 15.9998 9 15.9998C10.1046 15.9998 11 15.1044 11 13.9998C11 12.8953 10.1046 11.9998 9 11.9998ZM5 13.9998C5 11.7907 6.79086 9.99984 9 9.99984C11.2091 9.99984 13 11.7907 13 13.9998C13 16.209 11.2091 17.9998 9 17.9998C6.79086 17.9998 5 16.209 5 13.9998ZM15 10.9998H19V12.9998H15V10.9998ZM15 14.9998H19V16.9998H15V14.9998Z"}}]},g=d({name:"Radio1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-radio-1",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
