import{d,D as a,aa as O,ab as y,ac as C}from"./index-BfYxpJCx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7 14C7 12.8954 7.89543 12 9 12C10.1046 12 11 12.8954 11 14C11 15.1046 10.1046 16 9 16C7.89543 16 7 15.1046 7 14Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M23 21.9998H1V6.38181L12.4472 0.658203L13.3416 2.44706L6.23607 5.99985H23V21.9998ZM9 10C6.79086 10 5 11.7909 5 14C5 16.2091 6.79086 18 9 18C11.2091 18 13 16.2091 13 14C13 11.7909 11.2091 10 9 10ZM19 11H15V13H19V11ZM19 15H15V17H19V15Z"}}]},b=d({name:"Radio1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-radio-1-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(g,f.value)}});export{b as default};
