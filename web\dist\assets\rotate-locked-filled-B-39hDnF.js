import{d,D as a,aa as C,ab as O,ac as y}from"./index-BfYxpJCx.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 25 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.2263 3C7.25576 3 3.22632 7.02944 3.22632 12C3.22632 16.9706 7.25576 21 12.2263 21C15.9153 21 19.0882 18.7803 20.4779 15.5996L22.3106 16.4004C20.6141 20.2833 16.7386 23 12.2263 23C6.15119 23 1.22632 18.0751 1.22632 12C1.22632 5.92487 6.15119 1 12.2263 1C18.3015 1 23.2263 5.92487 23.2263 12V14L19.6263 11.3L20.8263 9.7L20.9545 9.79616C19.9716 5.891 16.4366 3 12.2263 3ZM12.2263 8.5C12.9167 8.5 13.4763 9.05964 13.4763 9.75V10.5H10.9763V9.75C10.9763 9.05964 11.536 8.5 12.2263 8.5ZM15.4763 10.5V9.75C15.4763 7.95508 14.0212 6.5 12.2263 6.5C10.4314 6.5 8.97632 7.95507 8.97632 9.75V10.5H7.72485V17H16.7249V10.5H15.4763Z"}}]},g=d({name:"RotateLockedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:s}=C(r),p=a(()=>["t-icon","t-icon-rotate-locked-filled",o.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};
