import{d,D as a,aa as O,ab as y,ac as m}from"./index-BfYxpJCx.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M19.0677 7.07007L12 0.0690918L4.93229 7.07007C1.02257 10.9429 1.02257 17.2259 4.93229 21.0987C8.83675 24.9663 15.1632 24.9663 19.0677 21.0987C22.9774 17.2259 22.9774 10.9429 19.0677 7.07007ZM18 13.9963C18 17.31 15.3137 19.9963 12 19.9963V7.99634C15.3137 7.99634 18 10.6826 18 13.9963Z"}}]},C=d({name:"SaturationFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-saturation-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(b,f.value)}});export{C as default};
