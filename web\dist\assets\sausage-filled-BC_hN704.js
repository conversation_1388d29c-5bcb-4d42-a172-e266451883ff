import{d,D as a,aa as O,ab as y,ac as g}from"./index-BfYxpJCx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M19.9983 2.00293V3.99941H22.0023V5.99941H20.7451C21.2068 7.45561 20.8602 9.11256 19.7054 10.2674L10.2656 19.7072C9.11077 20.862 7.45381 21.2086 5.99761 20.7469L5.99761 22.001H3.99761L3.99761 20.0001H2.00195V18.0001H3.2508C2.78915 16.5439 3.13571 14.887 4.2905 13.7322L13.7304 4.2923C14.8852 3.13751 16.5421 2.79095 17.9983 3.2526V2.00293H19.9983Z"}}]},C=d({name:"SausageFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-sausage-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{C as default};
