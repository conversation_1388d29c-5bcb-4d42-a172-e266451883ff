import{d,D as a,aa as O,ab as y,ac as V}from"./index-BfYxpJCx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){V(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 3H1V21H23V3ZM15 10.0476L16 9.33979V8H18V9.59847C18 10.0852 17.7638 10.5416 17.3666 10.8228L15.7034 12L17.3666 13.1772C17.7638 13.4584 18 13.9148 18 14.4015V16H16V14.6602L15 13.9524V16H13V8H15V10.0476ZM7 11.4286H9V8H11V16H9V13.4286H7C5.89543 13.4286 5 12.5331 5 11.4286V8H7V11.4286Z"}}]},g=d({name:"Screen4KFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=O(t),p=a(()=>["t-icon","t-icon-screen-4k-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{g as default};
