import{d as L,D as a,aa as y,ab as d,ac as O}from"./index-BfYxpJCx.js";function s(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function l(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?s(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.272 1V3.65018L19.8853 7.53907V15.2847L22.35 16.58L21.4197 18.3504L18.9126 17.0329L13.272 20.3498V23H11.272V20.3498L5.58289 17.0044L2.4625 18.3082L1.69141 16.4629L4.65873 15.223V7.53907L11.272 3.65018V1H13.272ZM12.272 5.38231L7.63156 8.11109L12.2723 10.8399L16.9126 8.11113L12.272 5.38231ZM17.8853 9.85928L13.2723 12.572L13.2721 18.0296L17.8853 15.3168V9.85928ZM11.2721 18.0297L11.2723 12.572L6.65873 9.85919V15.3168L11.2721 18.0297Z"}}]},g=L({name:"SystemCoordinateIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=y(r),p=a(()=>["t-icon","t-icon-system-coordinate",o.value]),u=a(()=>l(l({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
