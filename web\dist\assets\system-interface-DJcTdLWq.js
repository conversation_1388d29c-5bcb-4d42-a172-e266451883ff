import{d as y,D as a,aa as O,ab as m,ac as d}from"./index-BfYxpJCx.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function l(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2H22V22H2V2ZM4 9V20H20V9H4ZM20 7V4H4V7H20ZM6 11H8.00391V13.0039H6V11ZM10 11H12.0039V13.0039H10V11ZM14 11H16.0039V13.0039H14V11Z"}}]},H=y({name:"SystemInterfaceIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-system-interface",o.value]),u=a(()=>l(l({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:v})}}));return()=>m(b,f.value)}});export{H as default};
