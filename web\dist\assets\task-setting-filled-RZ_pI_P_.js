import{d as v,D as a,aa as d,ab as g,ac as O}from"./index-BfYxpJCx.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 25 25",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16 1H8V5H16V1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M6 3H3V23H13.8762C13.0139 21.897 12.5 20.5085 12.5 19C12.5 15.4101 15.4101 12.5 19 12.5C19.6978 12.5 20.3699 12.61 21 12.8135V3H18V7H6V3Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M19.9998 13.75V15.126C20.7147 15.31 21.3523 15.6867 21.8539 16.1975L23.0464 15.509L24.0464 17.241L22.8548 17.929C22.9493 18.2699 22.9998 18.629 22.9998 19C22.9998 19.371 22.9493 19.7301 22.8548 20.071L24.0464 20.759L23.0464 22.491L21.8539 21.8025C21.3523 22.3133 20.7147 22.69 19.9998 22.874V24.25H17.9998V22.874C17.2848 22.69 16.6472 22.3133 16.1457 21.8025L14.9531 22.491L13.9531 20.759L15.1447 20.071C15.0503 19.7301 14.9998 19.371 14.9998 19C14.9998 18.629 15.0503 18.2699 15.1447 17.929L13.9531 17.241L14.9531 15.509L16.1457 16.1975C16.6472 15.6867 17.2848 15.31 17.9998 15.126V13.75H19.9998ZM17.2485 18.0333C17.09 18.3198 16.9998 18.6494 16.9998 19C16.9998 19.3506 17.09 19.6802 17.2485 19.9667L17.2851 20.03C17.6349 20.6112 18.2719 21 18.9998 21C19.7276 21 20.3646 20.6112 20.7145 20.03L20.751 19.9668C20.9095 19.6802 20.9998 19.3507 20.9998 19C20.9998 18.6493 20.9095 18.3198 20.751 18.0332L20.7145 17.97C20.3646 17.3888 19.7276 17 18.9998 17C18.2719 17 17.6349 17.3888 17.285 17.97L17.2485 18.0333Z"}}]},m=v({name:"TaskSettingFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-task-setting-filled",l.value]),C=a(()=>s(s({},c.value),t.style)),u=a(()=>({class:p.value,style:C.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>g(y,u.value)}});export{m as default};
