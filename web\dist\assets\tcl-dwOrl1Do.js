const e=Object.freeze(JSON.parse('{"displayName":"Tcl","fileTypes":["tcl"],"foldingStartMarker":"\\\\{\\\\s*$","foldingStopMarker":"^\\\\s*}","name":"tcl","patterns":[{"begin":"(?<=^|;)\\\\s*((#))","beginCaptures":{"1":{"name":"comment.line.number-sign.tcl"},"2":{"name":"punctuation.definition.comment.tcl"}},"contentName":"comment.line.number-sign.tcl","end":"\\\\n","patterns":[{"match":"(\\\\\\\\[\\\\n\\\\\\\\])"}]},{"captures":{"1":{"name":"keyword.control.tcl"}},"match":"(?<=^|[;\\\\[{])\\\\s*(if|while|for|catch|default|return|break|continue|switch|exit|foreach|try|throw)\\\\b"},{"captures":{"1":{"name":"keyword.control.tcl"}},"match":"(?<=^|})\\\\s*(then|elseif|else)\\\\b"},{"captures":{"1":{"name":"keyword.other.tcl"},"2":{"name":"entity.name.function.tcl"}},"match":"(?<=^|\\\\{)\\\\s*(proc)\\\\s+(\\\\S+)"},{"captures":{"1":{"name":"keyword.other.tcl"}},"match":"(?<=^|[;\\\\[{])\\\\s*(after|append|array|auto_execok|auto_import|auto_load|auto_mkindex|auto_mkindex_old|auto_qualify|auto_reset|bgerror|binary|cd|clock|close|concat|dde|encoding|eof|error|eval|exec|expr|fblocked|fconfigure|fcopy|file|fileevent|filename|flush|format|gets|glob|global|history|http|incr|info|interp|join|lappend|library|lindex|linsert|list|llength|load|lrange|lreplace|lsearch|lset|lsort|memory|msgcat|namespace|open|package|parray|pid|pkg::create|pkg_mkIndex|proc|puts|pwd|re_syntax|read|registry|rename|resource|scan|seek|set|socket|SafeBase|source|split|string|subst|Tcl|tcl_endOfWord|tcl_findLibrary|tcl_startOfNextWord|tcl_startOfPreviousWord|tcl_wordBreakAfter|tcl_wordBreakBefore|tcltest|tclvars|tell|time|trace|unknown|unset|update|uplevel|upvar|variable|vwait)\\\\b"},{"begin":"(?<=^|[;\\\\[{])\\\\s*(reg(?:exp|sub))\\\\b\\\\s*","beginCaptures":{"1":{"name":"keyword.other.tcl"}},"end":"[]\\\\n;]","patterns":[{"match":"\\\\\\\\(?:.|\\\\n)","name":"constant.character.escape.tcl"},{"match":"-\\\\w+\\\\s*"},{"applyEndPatternLast":1,"begin":"--\\\\s*","end":"","patterns":[{"include":"#regexp"}]},{"include":"#regexp"}]},{"include":"#escape"},{"include":"#variable"},{"include":"#operator"},{"include":"#numeric"},{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.tcl"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end.tcl"}},"name":"string.quoted.double.tcl","patterns":[{"include":"#escape"},{"include":"#variable"},{"include":"#embedded"}]}],"repository":{"bare-string":{"begin":"(?:^|(?<=\\\\s))\\"","end":"\\"([^]\\\\s]*)","endCaptures":{"1":{"name":"invalid.illegal.tcl"}},"patterns":[{"include":"#escape"},{"include":"#variable"}]},"braces":{"begin":"(?:^|(?<=\\\\s))\\\\{","end":"}([^]\\\\s]*)","endCaptures":{"1":{"name":"invalid.illegal.tcl"}},"patterns":[{"match":"\\\\\\\\[\\\\n{}]","name":"constant.character.escape.tcl"},{"include":"#inner-braces"}]},"embedded":{"begin":"\\\\[","beginCaptures":{"0":{"name":"punctuation.section.embedded.begin.tcl"}},"end":"]","endCaptures":{"0":{"name":"punctuation.section.embedded.end.tcl"}},"name":"source.tcl.embedded","patterns":[{"include":"source.tcl"}]},"escape":{"match":"\\\\\\\\(\\\\d{1,3}|x\\\\h+|u\\\\h{1,4}|.|\\\\n)","name":"constant.character.escape.tcl"},"inner-braces":{"begin":"\\\\{","end":"}","patterns":[{"match":"\\\\\\\\[\\\\n{}]","name":"constant.character.escape.tcl"},{"include":"#inner-braces"}]},"numeric":{"match":"(?<![A-Za-z])([-+]?([0-9]*\\\\.)?[0-9]+f?)(?![.A-Za-z])","name":"constant.numeric.tcl"},"operator":{"match":"(?<=[ \\\\d])([-+~]|&{1,2}|\\\\|{1,2}|<{1,2}|>{1,2}|\\\\*{1,2}|[!%/]|<=|>=|={1,2}|!=|\\\\^)(?=[ \\\\d])","name":"keyword.operator.tcl"},"regexp":{"begin":"(?=\\\\S)(?![]\\\\n;])","end":"(?=[]\\\\n;])","patterns":[{"begin":"(?=[^\\\\t\\\\n ;])","end":"(?=[\\\\t\\\\n ;])","name":"string.regexp.tcl","patterns":[{"include":"#braces"},{"include":"#bare-string"},{"include":"#escape"},{"include":"#variable"}]},{"begin":"[\\\\t ]","end":"(?=[]\\\\n;])","patterns":[{"include":"#variable"},{"include":"#embedded"},{"include":"#escape"},{"include":"#braces"},{"include":"#string"}]}]},"string":{"applyEndPatternLast":1,"begin":"(?:^|(?<=\\\\s))(?=\\")","end":"","name":"string.quoted.double.tcl","patterns":[{"include":"#bare-string"}]},"variable":{"captures":{"1":{"name":"punctuation.definition.variable.tcl"}},"match":"(\\\\$)((?:[0-9A-Z_a-z]|::)+(\\\\([^)]+\\\\))?|\\\\{[^}]*})","name":"support.function.tcl"}},"scopeName":"source.tcl"}')),n=[e];export{n as default};
