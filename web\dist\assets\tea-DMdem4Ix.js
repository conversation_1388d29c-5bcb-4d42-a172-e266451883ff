import{d as O,D as a,aa as y,ab as C,ac as d}from"./index-BfYxpJCx.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var H={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.0004 2V7H9.00041V2H11.0004ZM7.00041 3V7H5.00041V3H7.00041ZM15.0004 3V7H13.0004V3H15.0004ZM2.92676 8H21.0004V13C21.0004 15.2091 19.2095 17 17.0004 17H16.0669C15.5284 18.2443 14.6199 19.2901 13.4843 20H20.0004V22H3.00041V20H6.50591C4.79741 18.9327 3.60555 17.1027 3.422 14.9613L3.42138 14.9541L2.92676 8ZM16.5754 15H17.0004C18.105 15 19.0004 14.1046 19.0004 13V10H16.9318L16.5794 14.9541L16.5788 14.9613C16.5777 14.9742 16.5766 14.9871 16.5754 15ZM14.9268 10H5.07406L5.41529 14.7974C5.6226 17.1749 7.61317 19 10.0004 19C12.0585 19 13.8359 17.6205 14.4124 15.711C14.5014 15.4162 14.5571 15.1245 14.5855 14.7975L14.9268 10Z"}}]},b=O({name:"TeaIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=y(r),p=a(()=>["t-icon","t-icon-tea",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>C(H,v.value)}});export{b as default};
