import{d,D as a,aa as O,ab as m,ac as y}from"./index-BfYxpJCx.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 3H5V4V11V12V13V20V21H6H14C16.7614 21 19 18.7614 19 16C19 14.123 17.9657 12.4875 16.4359 11.6325C17.3991 10.7211 18 9.43071 18 8C18 5.23858 15.7614 3 13 3H6ZM13 11H7V5H13C14.6569 5 16 6.34315 16 8C16 9.65685 14.6569 11 13 11ZM7 13H13H14C15.6569 13 17 14.3431 17 16C17 17.6569 15.6569 19 14 19H7V13Z"}}]},g=d({name:"TextformatBoldIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-textformat-bold",o.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>m(C,f.value)}});export{g as default};
