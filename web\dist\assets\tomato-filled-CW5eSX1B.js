import{d as v,D as a,aa as d,ab as O,ac as m}from"./index-BfYxpJCx.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.0254 4.12107L9.50378 1.58594L8.08579 2.99636L9.13573 4.05194C8.65416 4.0469 8.16446 4.08856 7.72517 4.21412C6.13836 4.66767 4.67724 5.47124 3.62223 6.85477C2.56694 8.23866 2.00016 10.095 2 12.5001C1.99983 15.0244 3.11154 17.5634 4.69339 19.3629C6.25049 21.1342 8.48948 22.4181 10.7874 21.8732C11.1426 21.7889 11.3995 21.7128 11.552 21.6677C11.5993 21.6536 11.6369 21.6425 11.6639 21.6351L11.6663 21.6345C11.7423 21.6137 11.7888 21.601 12 21.601C12.2112 21.601 12.2577 21.6137 12.3336 21.6345L12.3361 21.6351C12.3631 21.6425 12.4005 21.6536 12.448 21.6676C12.6004 21.7128 12.8574 21.7889 13.2126 21.8732C15.5179 22.4199 17.7575 21.1393 19.3143 19.3661C20.8949 17.5658 22.0013 15.0256 22 12.4996C21.9988 10.0848 21.4109 8.22969 20.3475 6.8498C19.2863 5.47275 17.8307 4.67171 16.2822 4.21625C15.8918 4.10142 15.4616 4.05412 15.0327 4.04781L16.1025 3.04788L14.7368 1.58674L12.0254 4.12107ZM12 8C9.7424 8 9 6 9 6C9 6 10.8754 6.63618 12 6.63618C13.1247 6.63618 15 6 15 6C15 6 14.2577 8 12 8Z"}}]},g=v({name:"TomatoFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=d(r),p=a(()=>["t-icon","t-icon-tomato-filled",o.value]),C=a(()=>s(s({},c.value),t.style)),u=a(()=>({class:p.value,style:C.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(y,u.value)}});export{g as default};
