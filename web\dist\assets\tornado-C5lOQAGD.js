import{d as f,D as a,aa as d,ab as O,ac as y}from"./index-BfYxpJCx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5.52778 4C5.21892 4 5 4.24108 5 4.5C5 4.75892 5.21892 5 5.52778 5L19 5L19 7L5.52778 7C4.1491 7 3 5.89793 3 4.5C3 3.10207 4.1491 2 5.52778 2L8 2L8 4L5.52778 4ZM21 8L21 10L6 10L6 8L21 8ZM8 11L19 11L19 13L8 13L8 11ZM6 14L15 14L15 16L6 16L6 14ZM5 17L13 17L13 19L5 19L5 17ZM8 20L15 20L15 22L8 22L8 20Z"}}]},g=f({name:"TornadoIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=d(t),p=a(()=>["t-icon","t-icon-tornado",o.value]),L=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:p.value,style:L.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>O(m,u.value)}});export{g as default};
