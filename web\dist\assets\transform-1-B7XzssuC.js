import{d as v,D as a,aa as O,ab as m,ac as y}from"./index-BfYxpJCx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2.00001 2L8 2V4L16 4L16 2L22 2V8L20 8L20 16H22V22H16L16 20L8 20V22H2L2 16H4L4 8H2L2.00001 2ZM6 8L6 16L8 16V18H16L16 16H18L18 8H16L16 6L8 6V8H6ZM6 4L4.00001 4L4 6H6L6 4ZM20 6V4H18V6L20 6ZM18 18V20H20V18L18 18ZM6 20V18H4L4 20H6Z"}}]},b=v({name:"Transform1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-transform-1",o.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:L=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:L})}}));return()=>m(d,f.value)}});export{b as default};
