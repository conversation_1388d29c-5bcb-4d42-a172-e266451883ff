import{d as f,D as a,aa as O,ab as y,ac as d}from"./index-BfYxpJCx.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 4C9.567 4 8 5.567 8 7.5C8 9.433 9.567 11 11.5 11C13.433 11 15 9.433 15 7.5C15 5.567 13.433 4 11.5 4ZM6 7.5C6 4.46243 8.46243 2 11.5 2C14.5376 2 17 4.46243 17 7.5C17 10.5376 14.5376 13 11.5 13C8.46243 13 6 10.5376 6 7.5ZM18 14C17.0694 14 16.5 14.6561 16.5 15.2489V16.2489H14.5V15.2489C14.5 13.3576 16.1692 12 18 12C19.8308 12 21.5 13.3576 21.5 15.2489C21.5 16.1644 21.0933 16.9746 20.4733 17.549L19 18.9388V19.6223H17V18.0759L19.1123 16.0834C19.3677 15.8476 19.5 15.5523 19.5 15.2489C19.5 14.6561 18.9306 14 18 14ZM8 16C5.79086 16 4 17.7909 4 20H12.8008V22H2V20C2 16.6863 4.68629 14 8 14H12.75V16H8ZM16.9991 20.9961H19.003V23H16.9991V20.9961Z"}}]},g=f({name:"UserUnknownIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-user-unknown",o.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(m,C.value)}});export{g as default};
