// 改进的脚本参数提取功能
// 这个文件包含了需要替换到ValueDialog.vue中的extractScriptParameters方法

// 提取脚本参数
const extractScriptParameters = () => {
  const script = data.value.scriptValue || '';
  const inputData = {};

  // 分析脚本中使用的变量
  const usedVariables = analyzeScriptVariables(script);
  
  // 根据分析结果构建输入数据
  usedVariables.forEach(varPath => {
    // 查找对应的变量定义
    const variable = findVariableByPath(varPath);
    if (variable) {
      setNestedValue(inputData, varPath, getSampleValue(variable.type));
    }
  });

  scriptTestInputData.value = inputData;
};

// 分析脚本中使用的变量
const analyzeScriptVariables = (script) => {
  const usedVariables = new Set();
  
  // 移除注释和字符串字面量，避免误匹配
  const cleanScript = script
    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
    .replace(/\/\/.*$/gm, '') // 移除单行注释
    .replace(/"[^"]*"/g, '""') // 移除双引号字符串
    .replace(/'[^']*'/g, "''") // 移除单引号字符串
    .replace(/`[^`]*`/g, '``'); // 移除模板字符串

  // 匹配临时变量（直接访问）
  // 匹配模式：变量名（字母开头，可包含字母、数字、下划线）
  const currentVarPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b/g;
  let match;
  
  while ((match = currentVarPattern.exec(cleanScript)) !== null) {
    const varPath = match[1];
    
    // 排除JavaScript关键字和内置对象
    if (!isJavaScriptKeyword(varPath) && !isBuiltinObject(varPath)) {
      // 检查是否是临时变量
      if (actionFlowStore.currentVariables.some(v => v.path === varPath || varPath.startsWith(v.path + '.'))) {
        usedVariables.add(varPath);
      }
    }
  }

  // 匹配局部变量（通过_data访问）
  // 匹配模式：_data.变量路径
  const localVarPattern = /_data\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
  
  while ((match = localVarPattern.exec(cleanScript)) !== null) {
    const varPath = match[1];
    // 检查是否是局部变量
    if (actionFlowStore.localVariables.some(v => v.path === varPath || varPath.startsWith(v.path + '.'))) {
      usedVariables.add('_data.' + varPath);
    }
  }

  return Array.from(usedVariables);
};

// 检查是否是JavaScript关键字
const isJavaScriptKeyword = (word) => {
  const keywords = [
    'break', 'case', 'catch', 'class', 'const', 'continue', 'debugger', 'default', 'delete',
    'do', 'else', 'export', 'extends', 'finally', 'for', 'function', 'if', 'import', 'in',
    'instanceof', 'let', 'new', 'return', 'super', 'switch', 'this', 'throw', 'try', 'typeof',
    'var', 'void', 'while', 'with', 'yield', 'true', 'false', 'null', 'undefined'
  ];
  return keywords.includes(word.split('.')[0]);
};

// 检查是否是内置对象
const isBuiltinObject = (word) => {
  const builtins = [
    'console', 'Math', 'Date', 'Array', 'Object', 'String', 'Number', 'Boolean', 'RegExp',
    'JSON', 'parseInt', 'parseFloat', 'isNaN', 'isFinite', 'Utils', 'window', 'document'
  ];
  return builtins.includes(word.split('.')[0]);
};

// 根据路径查找变量定义
const findVariableByPath = (varPath) => {
  // 处理_data前缀的局部变量
  if (varPath.startsWith('_data.')) {
    const localPath = varPath.substring(6); // 移除'_data.'前缀
    return actionFlowStore.localVariables.find(v => v.path === localPath || localPath.startsWith(v.path + '.'));
  }
  
  // 查找临时变量
  return actionFlowStore.currentVariables.find(v => v.path === varPath || varPath.startsWith(v.path + '.'));
};

// 在对象中设置嵌套值
const setNestedValue = (obj, path, value) => {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
};

// 获取示例值
const getSampleValue = (type) => {
  switch (type) {
    case 'string':
      return 'sample text';
    case 'number':
      return 123;
    case 'boolean':
      return true;
    case 'array':
      return [1, 2, 3];
    case 'object':
      return { key: 'value' };
    default:
      return null;
  }
};

// 使用说明：
// 1. 将上述代码中的extractScriptParameters方法替换ValueDialog.vue中的同名方法
// 2. 将其他辅助方法（analyzeScriptVariables, isJavaScriptKeyword等）添加到ValueDialog.vue的script部分
// 3. 这样就能智能分析脚本中实际使用的变量，而不是把所有变量都加进去

// 测试示例：
// 脚本内容：
// const result = equipmentId + "_" + timestamp;
// return Utils.formatDate(_data.currentTime);
//
// 分析结果：
// - equipmentId (临时变量)
// - timestamp (临时变量) 
// - _data.currentTime (局部变量)
//
// 生成的输入数据：
// {
//   "equipmentId": "sample text",
//   "timestamp": 123,
//   "_data": {
//     "currentTime": "sample text"
//   }
// }
