import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
  category?: string;
  categoryDisplayName?: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    defaultValue?: any;
    description?: string;
    isVariadic?: boolean;
  }>;
  returnType?: string;
  outputType?: string;
  examples?: Array<{
    title: string;
    code: string;
    result?: string;
    description: string;
  }>;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 使用模板字符串生成类型定义 - 支持完整的输入输出参数、描述等
const generateTypeDefinitionsWithTemplate = (variables: VariableData[], interfaceName: string): string => {
  // 收集所有嵌套接口定义
  const allNestedInterfaces: string[] = [];

  /**
   * 生成属性，同时收集嵌套接口定义
   */
  const generatePropertiesWithInterfaces = (vars: VariableData[], indent = '', parentPath = ''): string => {
    const properties: string[] = [];

    vars.forEach((variable) => {
      const { key, type, description, pathDescription, children } = variable;

      // 生成注释
      const comments: string[] = [];
      if (description) comments.push(description);
      if (pathDescription && pathDescription !== description) comments.push(`路径: ${pathDescription}`);
      if (type) comments.push(`类型: ${type}`);

      const commentBlock =
        comments.length > 0
          ? `${indent}/**\n${comments.map((c) => `${indent} * ${c}`).join('\n')}\n${indent} */\n`
          : '';

      // 处理嵌套对象
      if (children && children.length > 0) {
        const nestedInterfaceName = `${interfaceName}_${key}`;
        const nestedPath = parentPath ? `${parentPath}_${key}` : key;

        // 递归生成嵌套接口的属性
        const nestedProperties = generatePropertiesWithInterfaces(children, '  ', nestedPath);

        // 添加嵌套接口定义到全局收集器
        allNestedInterfaces.push(`interface ${nestedInterfaceName} {
${nestedProperties}
}`);

        // 添加属性引用
        properties.push(`${commentBlock}${indent}${key}: ${nestedInterfaceName};`);
      } else {
        // 普通属性
        const tsType = getTypeScriptType(type);
        properties.push(`${commentBlock}${indent}${key}: ${tsType};`);
      }
    });

    return properties.join('\n');
  };

  // 生成主接口属性
  const mainProperties = generatePropertiesWithInterfaces(variables);

  // 组合所有接口定义
  const allInterfaces = allNestedInterfaces.length > 0 ? `${allNestedInterfaces.join('\n\n')}\n\n` : '';

  return `${allInterfaces}interface ${interfaceName} {
${mainProperties}
}`;
};

// 使用模板字符串生成Utils类型定义 - 支持完整的参数描述
const generateUtilsTypeDefinition = (): string => {
  if (functions.length === 0) {
    return 'declare const Utils: any;';
  }

  const utilsMethods = functions
    .map((func) => {
      // 生成参数定义 - 使用模板字符串
      const generateParameters = (): string => {
        if (!func.parameters || func.parameters.length === 0) return '';

        return func.parameters
          .map((param) => {
            const paramType = getTypeScriptType(param.type);
            const optional = !param.required ? '?' : '';

            // 处理可变参数
            if (param.isVariadic) {
              return `...${param.name}: any[]`;
            } else {
              return `${param.name}${optional}: ${paramType}`;
            }
          })
          .join(', ');
      };

      // 生成返回类型
      const returnType = func.returnType ? getTypeScriptType(func.returnType) : 'any';

      // 生成完整的JSDoc注释 - 使用模板字符串
      const generateJSDoc = (): string => {
        const lines = ['  /**'];

        // 函数描述
        if (func.remark) {
          lines.push(`   * ${func.remark}`);
          if (func.label !== func.remark) {
            lines.push(`   * 中文名: ${func.label}`);
          }
        }

        // 参数说明
        if (func.parameters && func.parameters.length > 0) {
          lines.push('   *');
          func.parameters.forEach((param) => {
            const required = param.required ? '必需' : '可选';
            const defaultVal = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
            const desc = param.description || '';
            lines.push(`   * @param ${param.name} {${param.type}} ${required} - ${desc}${defaultVal}`);
          });
        }

        // 返回值说明
        if (func.returnType) {
          lines.push('   *');
          const outputDesc = func.outputType || func.returnType;
          lines.push(`   * @returns {${func.returnType}} ${outputDesc}`);
        }

        // 使用示例
        if (func.examples && func.examples.length > 0) {
          lines.push('   *');
          lines.push('   * @example');
          func.examples.slice(0, 2).forEach((example) => {
            lines.push(`   * // ${example.title}`);
            lines.push(`   * ${example.code}`);
            if (example.result) {
              lines.push(`   * // => ${example.result}`);
            }
            if (example.description) {
              lines.push(`   * // ${example.description}`);
            }
          });
        }

        lines.push('   */');
        return lines.join('\n');
      };

      const parameters = generateParameters();
      const jsdoc = generateJSDoc();

      return `${jsdoc}
  ${func.value}(${parameters}): ${returnType};`;
    })
    .join('\n\n');

  return `declare const Utils: {
${utilsMethods}
};`;
};

// 生成完整的 TypeScript 类型定义文件
const generateTypeDefinitionFile = (): string => {
  const sections: string[] = [];

  // 1. 生成嵌套接口定义
  const interfaces: string[] = [];

  if (localVariables.length > 0) {
    interfaces.push(generateTypeDefinitionsWithTemplate(localVariables, 'LocalVariables'));
  }

  if (globalVariables.length > 0) {
    interfaces.push(generateTypeDefinitionsWithTemplate(globalVariables, 'GlobalVariables'));
  }

  if (currentVariables.length > 0) {
    interfaces.push(generateTypeDefinitionsWithTemplate(currentVariables, 'CurrentVariables'));
  }

  if (interfaces.length > 0) {
    sections.push('// ===== 变量接口定义 =====');
    sections.push(...interfaces);
  }

  // 2. 生成 _data 对象类型定义
  if (localVariables.length > 0 || globalVariables.length > 0) {
    sections.push('// ===== 数据对象定义 =====');

    const dataProperties: string[] = [];

    // 处理局部变量 - 直接挂在 _data 下
    localVariables.forEach((variable) => {
      const { key, type, description, pathDescription, children } = variable;
      const comments = generateComments([description, pathDescription, `类型: ${type}`]);
      const tsType = children && children.length > 0 ? `LocalVariables_${key}` : getTypeScriptType(type);
      dataProperties.push(`${comments}  ${key}: ${tsType};`);
    });

    // 处理全局变量 - 挂在 _data._global 下
    if (globalVariables.length > 0) {
      const globalProperties: string[] = [];
      globalVariables.forEach((variable) => {
        const { key, type, description, pathDescription, children } = variable;
        const comments = generateComments([description, pathDescription, `类型: ${type}`]);
        const tsType = children && children.length > 0 ? `GlobalVariables_${key}` : getTypeScriptType(type);
        globalProperties.push(`${comments}    ${key}: ${tsType};`);
      });

      if (globalProperties.length > 0) {
        const globalComment = generateComments(['全局变量对象']);
        dataProperties.push(`${globalComment}  _global: {\n${globalProperties.join('\n')}\n  };`);
      }
    }

    if (dataProperties.length > 0) {
      sections.push(`interface DataObject {\n${dataProperties.join('\n')}\n}`);
      sections.push('declare const _data: DataObject;');
    }
  }

  // 3. 生成 Utils 工具类定义
  sections.push('// ===== 工具函数定义 =====');
  sections.push(generateUtilsTypeDefinition());

  // 4. 生成当前变量声明
  if (currentVariables.length > 0) {
    sections.push('// ===== 当前变量声明 =====');

    const declarations = currentVariables.map((variable) => {
      const { key, type, description, pathDescription, children } = variable;
      const comments = generateComments([description, pathDescription, `类型: ${type}`]);
      const tsType = children && children.length > 0 ? `CurrentVariables_${key}` : getTypeScriptType(type);
      return `${comments}declare const ${key}: ${tsType};`;
    });

    sections.push(...declarations);
  }

  return sections.filter((section) => section.trim()).join('\n\n');
};

// 生成注释块的辅助函数
const generateComments = (comments: (string | undefined)[]): string => {
  const validComments = comments.filter((c) => c && c.trim());
  if (validComments.length === 0) return '';

  return `  /**\n${validComments.map((c) => `   * ${c}`).join('\n')}\n   */\n`;
};

// 更新Monaco Editor的类型定义 - 使用原生 addExtraLib 方法
const updateMonacoTypeDefinitions = () => {
  try {
    // 生成完整的类型定义文件
    const typeDefinitions = generateTypeDefinitionFile();

    if (!typeDefinitions.trim()) {
      return;
    }

    // 清除之前的类型定义
    try {
      monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
      monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
    } catch (error) {
      console.warn('清除旧类型定义失败:', error);
    }

    // 使用原生 addExtraLib 方法添加类型定义
    const libUri = 'ts:custom-variables.d.ts';
    monaco.languages.typescript.typescriptDefaults.addExtraLib(typeDefinitions, libUri);
    monaco.languages.typescript.javascriptDefaults.addExtraLib(typeDefinitions, libUri);
  } catch (error) {
    console.error('更新Monaco Editor类型定义失败:', error);
  }
};

// 获取TypeScript类型字符串
const getTypeScriptType = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'string':
      return 'string';
    case 'number':
    case 'int':
    case 'integer':
    case 'float':
    case 'double':
      return 'number';
    case 'boolean':
    case 'bool':
      return 'boolean';
    case 'array':
      return 'any[]';
    default:
      return 'any';
  }
};

// 处理ROOT节点，展开其子节点（递归处理）
const expandRootNodes = (variables: VariableData[]): VariableData[] => {
  const result: VariableData[] = [];

  variables.forEach((variable) => {
    if (variable.key === 'ROOT' && variable.children && variable.children.length > 0) {
      // 如果是ROOT节点，递归展开其子节点
      const expandedChildren = expandRootNodes(variable.children);
      result.push(...expandedChildren);
    } else {
      // 非ROOT节点，递归处理其子节点
      const processedVariable = { ...variable };
      if (processedVariable.children && processedVariable.children.length > 0) {
        processedVariable.children = expandRootNodes(processedVariable.children);
      }
      result.push(processedVariable);
    }
  });

  return result;
};

// 生成外部字段的 .d.ts 定义文件
export const generateTypeDefinitionFromFields = (
  fields: Array<{
    name: string;
    type: string;
    description?: string;
    children?: any[];
  }>,
): string => {
  const interfaces: string[] = [];
  const declarations: string[] = [];

  // 递归生成接口定义
  const generateInterface = (fieldList: any[], interfaceName: string): string => {
    const properties = fieldList
      .map((field) => {
        const { name, type, description, children } = field;

        // 生成注释
        const comment = description ? `  /** ${description} */\n` : '';

        // 处理嵌套对象
        if (children && children.length > 0) {
          const childInterfaceName = `${interfaceName}_${name}`;
          interfaces.push(generateInterface(children, childInterfaceName));
          return `${comment}  ${name}: ${childInterfaceName};`;
        } else {
          const tsType = getTypeScriptType(type);
          return `${comment}  ${name}: ${tsType};`;
        }
      })
      .join('\n');

    return `interface ${interfaceName} {\n${properties}\n}`;
  };

  // 为每个顶级字段生成接口和声明
  fields.forEach((field) => {
    const { name, type, description, children } = field;

    if (children && children.length > 0) {
      // 生成嵌套接口
      const interfaceName = `${name.charAt(0).toUpperCase() + name.slice(1)}Type`;
      interfaces.push(generateInterface(children, interfaceName));

      // 生成变量声明
      const comment = description ? `/** ${description} */\n` : '';
      declarations.push(`${comment}declare const ${name}: ${interfaceName};`);
    } else {
      // 简单类型直接声明
      const tsType = getTypeScriptType(type);
      const comment = description ? `/** ${description} */\n` : '';
      declarations.push(`${comment}declare const ${name}: ${tsType};`);
    }
  });

  // 组合所有定义
  const allDefinitions = [...interfaces, ...declarations].join('\n\n');

  return `// 自动生成的类型定义文件\n// Generated TypeScript definitions\n\n${allDefinitions}`;
};

// 添加外部类型定义到 Monaco Editor
export const addExternalTypeDefinitions = (typeDefinitions: string, fileName: string = 'external-types.d.ts') => {
  try {
    const libUri = `ts:${fileName}`;
    monaco.languages.typescript.typescriptDefaults.addExtraLib(typeDefinitions, libUri);
    monaco.languages.typescript.javascriptDefaults.addExtraLib(typeDefinitions, libUri);
  } catch (error) {
    console.error('添加外部类型定义失败:', error);
  }
};

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    // 处理ROOT节点展开
    currentVariables = expandRootNodes(data.currentVariables);
  }
  if (data.localVariables) {
    // 处理ROOT节点展开
    localVariables = expandRootNodes(data.localVariables);
  }
  if (data.globalVariables) {
    // 处理ROOT节点展开
    globalVariables = expandRootNodes(data.globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
  }

  // 更新Monaco Editor的类型定义
  updateMonacoTypeDefinitions();
};

// 去重智能提示建议
const deduplicateSuggestions = (suggestions: any[]): any[] => {
  const seen = new Set<string>();
  return suggestions.filter((suggestion) => {
    const key = suggestion.label;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => ({
    label: variable.key,
    kind:
      variable.children && variable.children.length > 0
        ? monaco.languages.CompletionItemKind.Module
        : monaco.languages.CompletionItemKind.Variable,
    insertText: variable.key,
    detail: variable.type,
    documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
    sortText: `0_${variable.key}`,
  }));
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => {
    // 生成详细的文档说明
    const documentation = generateFunctionDocumentation(func);

    // 生成函数签名作为detail
    const signature = generateFunctionSignature(func);

    return {
      label: func.value, // 使用英文函数名而不是中文label
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: func.script,
      detail: signature, // 显示函数签名
      documentation: {
        value: documentation,
        isTrusted: true,
        supportHtml: true,
      },
      sortText: `1_${func.value}`,
    };
  });
};

// 生成函数签名
const generateFunctionSignature = (func: FunctionData): string => {
  let params = '';
  if (func.parameters && func.parameters.length > 0) {
    params = func.parameters
      .map((param) => {
        const optional = !param.required ? '?' : '';
        if (param.isVariadic) {
          return `...${param.name}: any[]`;
        } else {
          return `${param.name}${optional}: ${param.type}`;
        }
      })
      .join(', ');
  }

  const returnType = func.returnType || 'any';
  return `${func.value}(${params}): ${returnType}`;
};

// 生成函数文档
const generateFunctionDocumentation = (func: FunctionData): string => {
  const parts = [];

  // 添加中文名称和描述
  parts.push(`**${func.label}** - ${func.remark}`);

  // 添加函数签名
  const signature = generateFunctionSignature(func);
  parts.push(`\`\`\`typescript\n${signature}\n\`\`\``);

  // 添加参数说明
  if (func.parameters && func.parameters.length > 0) {
    parts.push('**参数说明:**');
    func.parameters.forEach((param) => {
      const required = param.required ? '必需' : '可选';
      const defaultValue = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
      parts.push(`- \`${param.name}\` (${param.type}, ${required}): ${param.description || ''}${defaultValue}`);
    });
  }

  // 添加返回值说明
  if (func.returnType) {
    parts.push(`**返回值:** ${func.outputType || func.returnType}`);
  }

  // 添加使用示例
  if (func.examples && func.examples.length > 0) {
    parts.push('**使用示例:**');
    func.examples.slice(0, 3).forEach((example, index) => {
      parts.push(`${index + 1}. **${example.title}**`);
      parts.push(`\`\`\`javascript\n${example.code}\n\`\`\``);
      if (example.result) {
        parts.push(`结果: \`${example.result}\``);
      }
      if (example.description) {
        parts.push(`说明: ${example.description}`);
      }
      parts.push('');
    });
  }

  return parts.join('\n\n');
};

// 检查是否应该提供自定义建议
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  if (triggerCharacter === '.') {
    return false;
  }

  const lastWord = trimmedText.split(/\s+/).pop() || '';

  const isTypingTopLevelVariable =
    lastWord.length > 0 &&
    lastWord.length <= 3 &&
    (currentVariables.some((v) => v.key.toLowerCase().startsWith(lastWord.toLowerCase())) ||
      lastWord.toLowerCase().includes('util') ||
      lastWord.toLowerCase().includes('data'));

  return isTypingTopLevelVariable;
};

// 注册简化的 TypeScript 智能提示提供者 - 主要依赖原生 addExtraLib
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    const trimmedText = textBeforePointer.trim();
    const triggerCharacter = context.triggerCharacter;

    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      const suggestions: any[] = [];

      const currentSuggestions = getFirstLevelSuggestions(currentVariables);
      suggestions.push(...currentSuggestions);

      const functionSuggestions = getFunctionSuggestions();
      suggestions.push(...functionSuggestions);

      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: true,
      };
    }

    return {
      suggestions: [],
      incomplete: true,
    };
  },
});

// 同样为 JavaScript 语言注册简化的智能提示提供者
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    const trimmedText = textBeforePointer.trim();
    const triggerCharacter = context.triggerCharacter;

    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      const suggestions: any[] = [];

      const currentSuggestions = getFirstLevelSuggestions(currentVariables);
      suggestions.push(...currentSuggestions);

      const functionSuggestions = getFunctionSuggestions();
      suggestions.push(...functionSuggestions);

      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: true,
      };
    }

    return {
      suggestions: [],
      incomplete: true,
    };
  },
});

// 提供Utils函数的参数提示
const provideUtilsSignatureHelp = (model: any, position: any) => {
  const { lineNumber, column } = position;

  // 获取当前行的文本
  const lineText = model.getLineContent(lineNumber);
  const textBeforePointer = lineText.substring(0, column - 1);

  // 查找Utils函数调用
  const utilsCallMatch = textBeforePointer.match(/Utils\.(\w+)\s*\(/);
  if (!utilsCallMatch) {
    return null;
  }

  const functionName = utilsCallMatch[1];
  const func = functions.find((f) => f.value === functionName);

  if (!func || !func.parameters || func.parameters.length === 0) {
    return null;
  }

  // 计算当前参数位置
  const functionCallStart = utilsCallMatch.index! + utilsCallMatch[0].length - 1; // 括号位置
  const currentParamText = textBeforePointer.substring(functionCallStart + 1);
  const commaCount = (currentParamText.match(/,/g) || []).length;

  const activeParameter = Math.min(commaCount, func.parameters.length - 1);

  // 生成参数信息
  const parameters = func.parameters.map((param) => {
    const isOptional = !param.required;
    const paramType = param.type === 'DateTime' ? 'Date' : param.type;

    let label: string;
    let documentation: string;

    if (param.isVariadic) {
      label = `...${param.name}: any[]`;
      documentation = param.description || `可变长度参数 ${param.name} - 可以传入任意数量的参数`;
    } else {
      label = `${param.name}${isOptional ? '?' : ''}: ${paramType}`;
      documentation = param.description || `参数 ${param.name}`;
    }

    return {
      label: label,
      documentation: {
        value: documentation,
        isTrusted: true,
      },
    };
  });

  // 生成函数签名
  const signature = {
    label: generateFunctionSignature(func),
    documentation: {
      value: `**${func.label}** - ${func.remark}`,
      isTrusted: true,
    },
    parameters: parameters,
    activeParameter: activeParameter,
  };

  return {
    value: {
      signatures: [signature],
      activeSignature: 0,
      activeParameter: activeParameter,
    },
    dispose: () => {},
  };
};

// 注册参数提示提供者 - TypeScript
monaco.languages.registerSignatureHelpProvider('typescript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});

// 注册参数提示提供者 - JavaScript
monaco.languages.registerSignatureHelpProvider('javascript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});
